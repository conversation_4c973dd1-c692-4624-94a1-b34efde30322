import {View, StyleSheet, ScrollView, Text} from 'react-native';
import React, {useEffect, useState} from 'react';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage} from '../../../../imilab-rn-sdk';
import {imiThemeManager, showToast, showLoading} from '../../../../imilab-design-ui';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import {stringsTo} from '../../../../globalization/Localize';
import {LetDevice} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDevice';

// 虚拟围栏物模型ID常量
const PROPERTY_IDS = {
  VIRTUAL_FENCE_DETECTION: '100000',  // 虚拟围栏检测开关
  VIRTUAL_FENCE_AREA_DATA: '100001',  // 虚拟围栏区域数据
  PERSON_ENTER_FENCE: '100002',       // 有人进入围栏开关
  PERSON_LEAVE_FENCE: '100003',       // 有人离开围栏开关
  SHOW_VIRTUAL_FENCE: '100004',       // 显示虚拟围栏
  PRIVACY_AREA_SWITCH: '100006',      // 区域隐私功能开关
  PRIVACY_AREA_DATA: '100007',        // 隐私区域
};

const VirtualFenceSetting = props => {
  // 状态管理
  const [virtualFenceEnabled, setVirtualFenceEnabled] = useState(false); // 开关UI状态
  const [virtualFenceActualEnabled, setVirtualFenceActualEnabled] = useState(false); // 真实设置状态，用于控制子组件显示
  const [personEnterFence, setPersonEnterFence] = useState(false);
  const [personLeaveFence, setPersonLeaveFence] = useState(false);
  const [showVirtualFence, setShowVirtualFence] = useState(false);
  const [fenceAreaData, setFenceAreaData] = useState(null); //虚拟围栏区域数据 100001
  const [privacyAreaSwitch, setPrivacyAreaSwitch] = useState(false); // 区域隐私功能开关 100006
  const [privacyAreaData, setPrivacyAreaData] = useState(null); // 隐私区域数据 100007

  // 获取所有虚拟围栏相关的物模型数据
  const loadVirtualFenceSettings = async () => {
    try {
      showLoading(stringsTo('commWaitText'), true);
      console.log('开始获取虚拟围栏设置...');

      // 使用Promise.all等待所有请求完成
      const promises = [
        LetDevice.getSingleProperty(PROPERTY_IDS.VIRTUAL_FENCE_DETECTION),
        LetDevice.getSingleProperty(PROPERTY_IDS.VIRTUAL_FENCE_AREA_DATA),
        LetDevice.getSingleProperty(PROPERTY_IDS.PERSON_ENTER_FENCE),
        LetDevice.getSingleProperty(PROPERTY_IDS.PERSON_LEAVE_FENCE),
        LetDevice.getSingleProperty(PROPERTY_IDS.SHOW_VIRTUAL_FENCE),
        LetDevice.getSingleProperty(PROPERTY_IDS.PRIVACY_AREA_SWITCH),
        LetDevice.getSingleProperty(PROPERTY_IDS.PRIVACY_AREA_DATA),
      ];

      const results = await Promise.allSettled(promises);

      // 处理虚拟围栏检测开关
      if (results[0].status === 'fulfilled') {
        const data = results[0].value;
        console.log('虚拟围栏检测开关--------' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          const isEnabled = data.value.value || false;
          setVirtualFenceEnabled(isEnabled);
          setVirtualFenceActualEnabled(isEnabled); // 同时设置真实状态
          console.log('虚拟围栏检测开关:', data.value.value);
        } else {
          // 默认值
          setVirtualFenceEnabled(false);
          setVirtualFenceActualEnabled(false);
        }
      } else {
        console.error('获取虚拟围栏检测开关失败:', results[0].reason);
        setVirtualFenceEnabled(false);
        setVirtualFenceActualEnabled(false);
      }

      // 处理虚拟围栏区域数据 (100001)
      if (results[1].status === 'fulfilled') {
        const data = results[1].value;
        console.log('=== 虚拟围栏区域数据 (100001) ===');
        console.log('完整返回数据:', JSON.stringify(data, null, 2));

        if (data?.value?.code == 0) {
          console.log('虚拟围栏区域数据获取成功');
          console.log('数据内容:', JSON.stringify(data.value.value, null, 2));
          setFenceAreaData(data.value.value);
        } else {
          console.log('虚拟围栏区域数据获取失败，code:', data?.value?.code);
          setFenceAreaData(null);
        }
        console.log('=== 虚拟围栏区域数据处理完成 ===');
      } else {
        console.error('获取虚拟围栏区域数据失败:', results[1].reason);
        setFenceAreaData(null);
      }

      // 处理有人进入围栏开关
      if (results[2].status === 'fulfilled') {
        const data = results[2].value;
        console.log('有人进入围栏开关--------' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          const isEnabled = data.value.value || false;
          setPersonEnterFence(isEnabled);
          console.log('有人进入围栏开关:', data.value.value);
        } else {
          // 默认值
          setPersonEnterFence(false);
        }
      } else {
        console.error('获取有人进入围栏开关失败:', results[2].reason);
        setPersonEnterFence(false);
      }

      // 处理有人离开围栏开关
      if (results[3].status === 'fulfilled') {
        const data = results[3].value;
        console.log('有人离开围栏开关--------' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          const isEnabled = data.value.value || false;
          setPersonLeaveFence(isEnabled);
          console.log('有人离开围栏开关:', data.value.value);
        } else {
          // 默认值
          setPersonLeaveFence(false);
        }
      } else {
        console.error('获取有人离开围栏开关失败:', results[3].reason);
        setPersonLeaveFence(false);
      }

      // 处理显示虚拟围栏开关
      if (results[4].status === 'fulfilled') {
        const data = results[4].value;
        console.log('显示虚拟围栏开关--------' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          const isEnabled = data.value.value || false;
          setShowVirtualFence(isEnabled);
          console.log('显示虚拟围栏开关:', data.value.value);
        } else {
          // 默认值
          setShowVirtualFence(false);
        }
      } else {
        console.error('获取显示虚拟围栏开关失败:', results[4].reason);
        setShowVirtualFence(false);
      }

      // 处理区域隐私功能开关 (100006)
      if (results[5].status === 'fulfilled') {
        const data = results[5].value;
        console.log('区域隐私功能开关--------' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          const isEnabled = data.value.value || false;
          setPrivacyAreaSwitch(isEnabled);
          console.log('区域隐私功能开关:', data.value.value);
        } else {
          // 默认值
          setPrivacyAreaSwitch(false);
        }
      } else {
        console.error('获取区域隐私功能开关失败:', results[5].reason);
        setPrivacyAreaSwitch(false);
      }

      // 处理隐私区域数据 (100007)
      if (results[6].status === 'fulfilled') {
        const data = results[6].value;
        console.log('=== 隐私区域数据 (100007) ===');
        console.log('完整返回数据:', JSON.stringify(data, null, 2));

        if (data?.value?.code == 0) {
          console.log('隐私区域数据获取成功');
          console.log('数据内容:', JSON.stringify(data.value.value, null, 2));
          setPrivacyAreaData(data.value.value);
        } else {
          console.log('隐私区域数据获取失败，code:', data?.value?.code);
          setPrivacyAreaData(null);
        }
        console.log('=== 隐私区域数据处理完成 ===');
      } else {
        console.error('获取隐私区域数据失败:', results[6].reason);
        setPrivacyAreaData(null);
      }

      // 检查是否有请求失败
      const hasFailures = results.some(result => result.status === 'rejected');
      if (hasFailures) {
        console.error('部分虚拟围栏设置获取失败');
        showToast(stringsTo('commLoadingFailText'));
      }

      console.log('虚拟围栏设置加载完成');
    } catch (error) {
      console.error('获取虚拟围栏设置失败:', error);
      showToast(stringsTo('commLoadingFailText'));
    } finally {
      showLoading(false);
    }
  };

  useEffect(() => {
    loadVirtualFenceSettings();
  }, []);

  // 设置物模型属性的通用函数
  const setVirtualFenceProperty = async (propertyId, value, stateSetter, propertyName, currentValue, isMainSwitch = false) => {
    // 立即更新UI状态，给用户即时反馈
    stateSetter(value);

    try {
      showLoading(stringsTo('commWaitText'), true);
      console.log(`设置${propertyName}:`, value);

      const paramJson = JSON.stringify({ msg_id: propertyId, value: value });

      await LetDevice.setProperties(true, LetDevice.deviceID, propertyId, paramJson);

      // 设置成功
      if (isMainSwitch) {
        // 如果是主开关，设置成功后更新真实状态，控制子组件显示
        setVirtualFenceActualEnabled(value);
      }
      showToast(stringsTo('settings_set_success'));
      console.log(`${propertyName}设置成功:`, value);

    } catch (error) {
      console.error(`设置${propertyName}失败:`, error);
      showToast(stringsTo('operationFailed'));
      // 设置失败时恢复到原来的状态
      stateSetter(currentValue);
      console.log(`${propertyName}设置失败，恢复到原状态:`, currentValue);
    } finally {
      showLoading(false);
    }
  };

  const handleVirtualFenceChange = (value) => {
    setVirtualFenceProperty(
      PROPERTY_IDS.VIRTUAL_FENCE_DETECTION,
      value,
      setVirtualFenceEnabled,
      '虚拟围栏检测开关',
      virtualFenceEnabled,
      true // 标记为主开关
    );
  };

  const handlePersonEnterChange = (value) => {
    setVirtualFenceProperty(
      PROPERTY_IDS.PERSON_ENTER_FENCE,
      value,
      setPersonEnterFence,
      '有人进入围栏开关',
      personEnterFence
    );
  };

  const handlePersonLeaveChange = (value) => {
    setVirtualFenceProperty(
      PROPERTY_IDS.PERSON_LEAVE_FENCE,
      value,
      setPersonLeaveFence,
      '有人离开围栏开关',
      personLeaveFence
    );
  };

  const handleShowVirtualFenceChange = (value) => {
    setVirtualFenceProperty(
      PROPERTY_IDS.SHOW_VIRTUAL_FENCE,
      value,
      setShowVirtualFence,
      '显示虚拟围栏开关',
      showVirtualFence
    );
  };

  const handlePrivacyAreaSwitchChange = (value) => {
    setVirtualFenceProperty(
      PROPERTY_IDS.PRIVACY_AREA_SWITCH,
      value,
      setPrivacyAreaSwitch,
      '区域隐私功能开关',
      privacyAreaSwitch
    );
  };

  const handleFenceAreaPress = () => {
    // 跳转到围栏区域设置页面
    console.log('跳转到围栏区域设置');
    // 模拟隐私区域数据（应该在围栏内部）
    const privacyAreaData = [50, 40, 200, 120];

    props.navigation.push('VirtualFenceAreaSetting', {
      areaData: fenceAreaData, // 围栏区域坐标
      privateSwitch: true, // 隐私区域开关
      privateAreaData: privacyAreaData, // 隐私区域坐标
      styleType: 2, // 可爱2样式
      callback: (areaData, areaType) => {
        console.log('围栏区域编辑回调:', areaData, areaType);
        setFenceAreaData(areaData); // 更新围栏区域数据
        // TODO: 这里可以添加保存围栏区域到物模型的逻辑
      }
    });
  };

  return (
    <View style={styles.container}>
      <NavigationBar
        title={stringsTo("virtual_Fence")}
        left={[
          {
            key: NavigationBar.ICON.BACK,
            onPress: () => {
              props.navigation.canGoBack() ? props.navigation.goBack() : IMIGotoPage.exit();
            },
            accessibilityLabel: 'virtual_fence_back',
          },
        ]}
        right={[]}
      />

      <ScrollView style={styles.scrollView}>
        {/* 设置选项区域 */}
        <View style={styles.settingsSection}>
          {/* 虚拟围栏主开关 */}
          <ListItmeWithSwitch 
            title="虚拟围栏"
            value={virtualFenceEnabled}
            onValueChange={handleVirtualFenceChange}
            accessibilityLabel={['virtual_fence_off', 'virtual_fence_on']}
          />
          
          {/* 有人进入围栏 */}
          {virtualFenceActualEnabled && (
            <ListItmeWithSwitch
              title="有人进入围栏"
              subtitle="当有人进入围栏时，拍摄视频并推送"
              value={personEnterFence}
              onValueChange={handlePersonEnterChange}
              accessibilityLabel={['person_enter_off', 'person_enter_on']}
            />
          )}

          {/* 有人离开围栏 */}
          {virtualFenceActualEnabled && (
            <ListItmeWithSwitch
              title="有人离开围栏"
              subtitle="当有人离开围栏时，拍摄视频并推送"
              value={personLeaveFence}
              onValueChange={handlePersonLeaveChange}
              accessibilityLabel={['person_leave_off', 'person_leave_on']}
            />
          )}

          {/* 围栏区域设置 */}
          {virtualFenceActualEnabled && (
            <ListItem
              title="围栏区域"
              onPress={handleFenceAreaPress}
              accessibilityLabel="fence_area_setting"
            />
          )}

          {/* 显示虚拟围栏 */}
          {virtualFenceActualEnabled && (
            <ListItmeWithSwitch
              title="显示虚拟围栏"
              value={showVirtualFence}
              onValueChange={handleShowVirtualFenceChange}
              accessibilityLabel={['show_fence_off', 'show_fence_on']}
            />
          )}

          {/* 区域隐私功能开关 */}
          {virtualFenceActualEnabled && (
            <ListItmeWithSwitch
              title="区域隐私功能"
              subtitle="开启后可设置隐私区域，该区域内容不会被录制"
              value={privacyAreaSwitch}
              onValueChange={handlePrivacyAreaSwitchChange}
              accessibilityLabel={['privacy_area_off', 'privacy_area_on']}
            />
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg || '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  settingsSection: {
    marginTop: 20,
  },
});

export default VirtualFenceSetting;
